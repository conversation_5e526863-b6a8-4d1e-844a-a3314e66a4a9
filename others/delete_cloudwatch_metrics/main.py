import boto3
import json

cloudwatch = boto3.client('cloudwatch')

NAMESPACE = "auth"  # Replace with your actual namespace
DIMENSION_KEY_PREFIX = "auth"

def list_metrics():
    metrics = cloudwatch.list_metrics(Namespace=NAMESPACE, MetricName="challenge.verify.success.count")
    print(json.dumps(metrics, indent=2))

    for metric in metrics["Metrics"]:
        print(metric["MetricName"])

    return metrics["Metrics"]

## Cloudwatch não permite deletar metrica
## https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/cloudwatch_concepts.html#metrics-retention
def delete_metric_data(metrics):
    for metric in metrics:
        dimensions = metric['Dimensions']
        try:
            # response = cloudwatch.delete_metric_data(
            #     MetricData=[
            #         {
            #             'MetricName': metric['MetricName'],
            #             'Dimensions': dimensions,
            #             'Timestamp': datetime.datetime.utcnow(),
            #             'Value': 0,
            #             'Unit': 'None'
            #         }
            #     ]
            # )
            cloudwatch.delete_metric_stream()
            print(f"Deleted metric data for {metric['MetricName']} with dimensions {dimensions}")
        except Exception as e:
            print(f"Error deleting {metric['MetricName']}: {e}")


if __name__ == "__main__":
    import datetime

    print("Scanning for metrics with dimension starting with 'auth'...")
    auth_metrics = list_metrics()

    print(f"Found {len(auth_metrics)} metric(s) to clean.")

    #delete_metric_data(auth_metrics)
