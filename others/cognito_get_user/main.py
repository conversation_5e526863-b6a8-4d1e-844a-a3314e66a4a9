#!/usr/bin/env python3

import argparse
import sys
import boto3
from botocore.exceptions import ClientError, BotoCoreError
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

console = Console()

def get_cognito_client():
    """Create and return a Cognito Identity Provider client"""
    try:
        return boto3.client('cognito-idp', region_name='us-east-1')
    except Exception as e:
        console.print(f"[red]Error creating Cognito client: {e}[/red]")
        sys.exit(1)

def get_current_user(access_token):
    """Get current user information using the access token"""
    client = get_cognito_client()
    try:
        response = client.get_user(AccessToken=access_token)
        return response
    except ClientError as e:
        console.print(f"[red]Error getting user: {e.response['Error']['Message']}[/red]")
        sys.exit(1)
    except BotoCoreError as e:
        console.print(f"[red]Error with AWS service: {e}[/red]")
        sys.exit(1)

def update_user_attribute(access_token, new_account_uuid):
    """Update the custom:accountUuid attribute for the user"""
    client = get_cognito_client()
    try:
        response = client.update_user_attributes(
            AccessToken=access_token,
            UserAttributes=[
                {
                    'Name': 'custom:accountUuid',
                    'Value': new_account_uuid
                }
            ]
        )
        return response
    except ClientError as e:
        console.print(f"[red]Error updating user attributes: {e.response['Error']['Message']}[/red]")
        sys.exit(1)
    except BotoCoreError as e:
        console.print(f"[red]Error with AWS service: {e}[/red]")
        sys.exit(1)

def find_account_uuid(user_attributes):
    """Find the current custom:accountUuid value from user attributes"""
    for attr in user_attributes:
        if attr.get('Name') == 'custom:accountUuid':
            return attr.get('Value')
    return None

def main():
    parser = argparse.ArgumentParser(description='Update Cognito user custom:accountUuid attribute')
    parser.add_argument('--access-token', help='Cognito access token')
    parser.add_argument('--new-account-uuid', help='New value for custom:accountUuid')

    args = parser.parse_args()

    console.print(Panel.fit("🔐 Cognito User Attribute Updater", style="bold blue"))

    # Get current user information
    console.print("\n[yellow]Fetching current user information...[/yellow]")
    user_info = get_current_user(args.access_token)

    # Extract current custom:accountUuid value
    current_account_uuid = find_account_uuid(user_info.get('UserAttributes', []))

    # Log current value
    console.print("\n[bold]Current User Information:[/bold]")
    console.print(f"Username: {user_info.get('Username', 'N/A')}")

    if current_account_uuid:
        console.print(f"Current custom:accountUuid: [green]{current_account_uuid}[/green]")
    else:
        console.print("Current custom:accountUuid: [red]Not set[/red]")

    # Update the attribute
    new_uuid = getattr(args, 'new_account_uuid')
    console.print(f"\n[yellow]Updating custom:accountUuid to: [bold]{new_uuid}[/bold][/yellow]")

    try:
        update_user_attribute(args.access_token, new_uuid)
        console.print("\n[green]✅ Successfully updated custom:accountUuid![/green]")

        # Verify the update by fetching user info again
        console.print("\n[yellow]Verifying update...[/yellow]")
        updated_user_info = get_current_user(args.access_token)
        new_account_uuid = find_account_uuid(updated_user_info.get('UserAttributes', []))

        if new_account_uuid == new_uuid:
            console.print(f"[green]✅ Verification successful! New value: {new_account_uuid}[/green]")
        else:
            console.print(f"[red]❌ Verification failed! Expected: {new_uuid}, Got: {new_account_uuid}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Failed to update attribute: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    main()
