from UnleashClient import UnleashClient
import os

def get_env_config(env):
    if env == "staging":
        return {
            "url": "https://unleash.staging.magie.services/api",
            "token": os.getenv("UNLEASH_STAGING_TOKEN")
        }
    elif env == "prod":
        return {
            "url": "https://unleash.giro.services/api",
            "token": os.getenv("UNLEASH_PROD_TOKEN")
        }
    else:
        raise ValueError("Invalid environment")


env = "prod"

config = get_env_config(env)
url = config["url"]
token = config["token"]

client = UnleashClient(
    url=url,
    app_name="auth",
    environment="production",
    custom_headers={"Authorization": token}
)
client.initialize_client()

userId1 = "4cb58518-fb66-4767-912d-ba16eae7b4e2"
userId2 = "adce9de6-6543-4693-afab-29ab7d5508dd"

response1 = client.is_enabled("app_disable_mfa_liveness", context={"userId": userId1})
response2 = client.is_enabled("app_disable_mfa_liveness", context={"userId": userId2})

print(f"{userId1}: {response1}")
print(f"{userId2}: {response2}")
