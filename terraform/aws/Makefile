.PHONY: clean-lock-cache plan-all

clean-lock-cache:
	@if [ -z "$(env)" ]; then \
		echo "Error: env parameter is required. Usage: make clean-lock-cache env=staging|prod"; \
		exit 1; \
	fi
	@if [ "$(env)" != "staging" ] && [ "$(env)" != "prod" ]; then \
		echo "Error: env must be 'staging' or 'prod'"; \
		exit 1; \
	fi
	@echo "Cleaning .terragrunt-cache directories in $(env) environment..."
	@find ./live/$(env) -name ".terragrunt-cache" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "Cleaning .terraform.lock.hcl files in $(env) environment..."
	@find ./live/$(env) -name ".terraform.lock.hcl" -type f -delete 2>/dev/null || true
	@echo "Cleanup completed for $(env) environment"

plan-all:
	@if [ -z "$(env)" ]; then \
		echo "Error: env parameter is required. Usage: make plan-all-staging env=staging|prod"; \
		exit 1; \
	fi
	@if [ "$(env)" != "staging" ] && [ "$(env)" != "prod" ]; then \
		echo "Error: env must be 'staging' or 'prod'"; \
		exit 1; \
	fi
	@echo "Running terragrunt plan for all modules in $(env) environment..."
	AWS_PROFILE=$(env) terragrunt run --all plan --working-dir ./live/$(env)
