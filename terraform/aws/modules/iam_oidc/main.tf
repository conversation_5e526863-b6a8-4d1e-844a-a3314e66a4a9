data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

resource "aws_iam_openid_connect_provider" "oidc_provider" {
  url             = var.url
  client_id_list  = var.client_id_list

  tags = var.tags
}

resource "aws_iam_role" "oidc_role" {
  name               = var.role_name
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = aws_iam_openid_connect_provider.oidc_provider.arn
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
            StringLike = {
              "token.actions.githubusercontent.com:sub" = [
                "repo:GiroOfficial/iac:*"
              ]
            }
        }
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_policy" "oidc_policy" {
  name        = "${var.role_name}-policy"
  description = "Policy for ${var.role_name}"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = [
          "acm:Describe*",
          "acm:Get*",
          "acm:List*",
          "cloudfront:Get*",
          "cloudfront:List*",
          "cognito-idp:Describe*",
          "cognito-idp:Get*",
          "dynamodb:*",
          "ec2:Describe*",
          "events:Describe*",
          "events:List*",
          "events:Put*",
          "events:TagResource",
          "iam:CreatePolicyVersion",
          "iam:CreateRole",
          "iam:Get*",
          "iam:List*",
          "lambda:*",
          "logs:CreateLogGroup",
          "logs:Describe*",
          "logs:List*",
          "route53:Get*",
          "route53:List*",
          "s3:*",
          "secretsmanager:*"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "oidc_policy_attachment" {
  role       = aws_iam_role.oidc_role.name
  policy_arn = aws_iam_policy.oidc_policy.arn
}
