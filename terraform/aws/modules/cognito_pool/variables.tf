variable "environment" {
  description = "Environment name"
  type        = string
}

variable "pool_name" {
  description = "Name of the Cognito User Pool"
  type        = string
}

variable "plan" {
  description = "Cognito User Pool plan"
  type        = string
  default     = "essentials"
  validation {
    condition     = contains(["essentials", "plus"], var.plan)
    error_message = "Plan must be either 'essentials' or 'plus'."
  }
}

variable "email_configuration" {
  description = "Email configuration for the Cognito User Pool"
  type = object({
    email_sending_account  = optional(string, "COGNITO_DEFAULT")
    from_email_address     = optional(string)
    reply_to_email_address = optional(string)
    source_arn             = optional(string)
  })
  default = {
    email_sending_account = "COGNITO_DEFAULT"
  }
}

variable "app_clients" {
  description = "List of app clients to create"
  type = list(object({
    client_name            = string
    generate_secret        = optional(bool, false)
    auth_session_validity  = optional(number, 3)
    access_token_validity  = optional(number, 60)
    id_token_validity      = optional(number, 60)
    refresh_token_validity = optional(number, 5)
    explicit_auth_flows    = optional(list(string), ["ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_CUSTOM_AUTH"])
  }))
  default = []
}

variable "enable_mfa" {
  description = "Enable MFA for the user pool"
  type        = bool
  default     = false
}

variable "mfa_configuration" {
  description = "MFA configuration"
  type        = string
  default     = "OPTIONAL"
  validation {
    condition     = contains(["OFF", "ON", "OPTIONAL"], var.mfa_configuration)
    error_message = "MFA configuration must be OFF, ON, or OPTIONAL."
  }
}

variable "password_policy" {
  description = "Password policy configuration for the user pool"
  type = object({
    minimum_length                   = optional(number, 8)
    require_lowercase                = optional(bool, true)
    require_numbers                  = optional(bool, true)
    require_symbols                  = optional(bool, true)
    require_uppercase                = optional(bool, true)
    temporary_password_validity_days = optional(number, 7)
  })
  default = {
    minimum_length                   = 8
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = true
    require_uppercase                = true
    temporary_password_validity_days = 7
  }
  validation {
    condition     = var.password_policy.minimum_length >= 6 && var.password_policy.minimum_length <= 99
    error_message = "Password minimum length must be between 6 and 99."
  }
}

variable "auto_verified_attributes" {
  description = "Attributes to be auto-verified. Valid values: email, phone_number"
  type        = list(string)
  default     = []
  validation {
    condition = alltrue([
      for attr in var.auto_verified_attributes : contains(["email", "phone_number"], attr)
    ])
    error_message = "Auto verified attributes must be either 'email' or 'phone_number'."
  }
}

variable "allow_admin_create_user_only" {
  description = "Set to true if only the administrator is allowed to create user profiles"
  type        = bool
  default     = false
}

variable "lambda_triggers" {
  description = "Lambda triggers for Cognito User Pool"
  type = object({
    define_auth_challenge = optional(string)
    create_auth_challenge = optional(string)
    verify_auth_challenge = optional(string)
    pre_token_generation_config = optional(object({
      lambda_arn     = string
      lambda_version = string
    }))
  })
  default = {}
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "custom_attributes" {
  description = "Custom attributes for the user pool"
  type = list(object({
    name                        = string
    attribute_data_type         = string
    mutable                     = bool
    required                    = bool
    string_attribute_constraints = object({
      min_length = number
      max_length = number
    })
  }))
  default = []
}
