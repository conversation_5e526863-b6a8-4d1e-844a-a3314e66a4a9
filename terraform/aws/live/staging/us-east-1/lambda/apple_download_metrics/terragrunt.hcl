include {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../../../../modules/lambda"
}

inputs = {
  function_name = "apple_download_metrics"
  description   = "Lambda function that fetch apple download metrics and push to datadog"
  runtime       = "python3.13"
  handler       = "main.lambda_handler"
  timeout       = 60
  memory_size   = 128
  source_path   = "./src"
  environment   = "staging"
  vpc_id        = "vpc-0c81cfcee3629b43a"

  enable_eventbridge_schedule = true
  schedule_expression         = "cron(0 13 * * ? *)"
  enable_secrets_manager      = true

  architectures=["x86_64"]

  tags = {
    product = "internal_tool"
    team    = "foundation"
  }
}
