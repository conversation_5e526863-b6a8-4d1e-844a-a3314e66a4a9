import json


def lambda_handler(event, context):
    """
    Cognito Pre Token Generation Lambda function to suppress admin scope.
    
    This function removes the 'aws.cognito.signin.user.admin' scope from access tokens
    to prevent users from calling admin APIs directly.
    
    AWS Documentation: https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-lambda-pre-token-generation.html#aws-lambda-triggers-pre-token-generation-example-version-2-overview
    """
    
    try:
        event['response'] = {
            'claimsAndScopeOverrideDetails': {
                'accessTokenGeneration': {
                    'scopesToSuppress': [
                        'aws.cognito.signin.user.admin'
                    ]
                }
            }
        }
        return event
    except Exception as e:
        print(f"Error in pre-token generation function", extra={
            'error': str(e),
            'event': event,
        })
        return event
