include {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../../../../modules/cognito_pool"
}

dependency "define_auth_challenge" {
  config_path = "../lambda/define_auth_challenge"

  mock_outputs = {
      lambda_function_arn = "arn:aws:lambda:us-east-1:************:function:define_auth_challenge"
  }
}

dependency "create_auth_challenge" {
  config_path = "../lambda/create_auth_challenge"

  mock_outputs = {
      lambda_function_arn = "arn:aws:lambda:us-east-1:************:function:create_auth_challenge"
  }
}

dependency "verify_auth_challenge" {
  config_path = "../lambda/verify_auth_challenge"

  mock_outputs = {
      lambda_function_arn = "arn:aws:lambda:us-east-1:************:function:verify_auth_challenge"
  }
}

dependency "pre_token_generation" {
  config_path = "../lambda/pre_token_generation"

  mock_outputs = {
      lambda_function_arn = "arn:aws:lambda:us-east-1:************:function:pre_token_generation"
  }
}

inputs = {
  environment = "staging"
  pool_name   = "mobile-staging"
  plan        = "essentials"

  allow_admin_create_user_only = true

  email_configuration = {
    email_sending_account = "COGNITO_DEFAULT"
  }

  app_clients = [
    {
      client_name            = "auth-service"
      auth_session_validity  = 15
      access_token_validity  = 600
      refresh_token_validity = 30
    }
  ]

  password_policy = {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_symbols   = true
    require_uppercase = true
  }

  custom_attributes = [
    {
      name                        = "accountUuid"
      attribute_data_type         = "String"
      mutable                     = true
      required                    = false
      string_attribute_constraints = {
        min_length = 1
        max_length = 256
      }
    }
  ]

  lambda_triggers = {
    define_auth_challenge = dependency.define_auth_challenge.outputs.lambda_function_arn
    create_auth_challenge = dependency.create_auth_challenge.outputs.lambda_function_arn
    verify_auth_challenge = dependency.verify_auth_challenge.outputs.lambda_function_arn
    pre_token_generation_config = {
      lambda_arn     = dependency.pre_token_generation.outputs.lambda_function_arn
      lambda_version = "V3_0"
    }
  }

  tags = {
    product = "app"
    team    = "foundation"
  }
}
