remote_state {
  backend = "s3"
  config = {
    bucket         = "magie-iac-state-staging"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    use_lockfile   = true
  }
}

generate "providers" {
  path      = "providers.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
    terraform {
      required_providers {
        aws = {
          source  = "hashicorp/aws"
          version = "~> 6.3"
        }
      }
    }

    provider "aws" {
      region = "us-east-1"

      default_tags {
        tags = {
          env = "staging"
          managed_by = "terraform"
        }
      }
    }
  EOF
}
